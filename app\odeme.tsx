import { Stack, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, Dimensions, Image, Platform, ScrollView, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import NfcManager, { NfcTech } from 'react-native-nfc-manager';

const { width } = Dimensions.get('window');

function parseCardDataFromText(text) {
  // "isim:Zeli<PERSON>;kartNo:1234;ay:12;yil:27;ccv:999" -> {isim, kartNo, ay, yil, ccv}
  const data = {};
  text.split(';').forEach(part => {
    const [key, value] = part.split(':');
    if (key && value) data[key.trim()] = value.trim();
  });
  return data;
}

export default function OdemeScreen() {
  const router = useRouter();
  const [isim, setIsim] = useState('');
  const [kartNo, setKartNo] = useState('');
  const [ay, setAy] = useState('');
  const [yil, setYil] = useState('');
  const [ccv, setCcv] = useState('');
  const [nfcData, setNfcData] = useState(null);

  useEffect(() => {
    // NFC Manager'ı başlat
    if (Platform.OS !== 'web') {
      NfcManager.start().catch(() => {
        console.log('NFC başlatılamadı');
      });
    }
    return () => {
      NfcManager.cancelTechnologyRequest().catch(() => {});
    };
  }, []);

  const handleNfcPayment = async () => {
    if (Platform.OS === 'web') {
      Alert.alert('Uyarı', 'NFC sadece mobil cihazda çalışır!');
      return;
    }

    try {
      await NfcManager.start();
      const isSupported = await NfcManager.isSupported();
      if (!isSupported) {
        Alert.alert('Uyarı', 'Bu cihaz NFC desteklemiyor!');
        return;
      }
      const isEnabled = await NfcManager.isEnabled();
      if (!isEnabled) {
        Alert.alert('Uyarı', 'NFC kapalı! Lütfen ayarlardan NFC\'yi açın.');
        return;
      }

      Alert.alert('NFC Tarama', 'NFC kartınızı cihaza yaklaştırın...', [
        {
          text: 'İptal',
          onPress: () => {
            NfcManager.cancelTechnologyRequest().catch(() => {});
          },
          style: 'cancel'
        }
      ]);

      await NfcManager.requestTechnology(NfcTech.Ndef);
      const tag = await NfcManager.getTag();

      if (tag?.ndefMessage?.length > 0) {
        // NFC NDEF payload'u çözümle
        const payload = tag.ndefMessage[0].payload;
        // Genelde ilk 3 byte meta, sonrakiler text
        const text = String.fromCharCode.apply(null, payload.slice(3));
        // Örneğin: "isim:Zeliha;kartNo:1234;ay:12;yil:27;ccv:999"
        const parsed = parseCardDataFromText(text);

        setIsim(parsed.isim || '');
        setKartNo(parsed.kartNo || '');
        setAy(parsed.ay || '');
        setYil(parsed.yil || '');
        setCcv(parsed.ccv || '');
        setNfcData(parsed);

        Alert.alert(
          'NFC Başarılı!',
          `Kart Bilgileri Okundu:\n\n${text}`,
          [{ text: 'Tamam' }]
        );
      } else {
        Alert.alert('NFC Hatası', 'Kart algılanamadı veya bilgi okunamadı.');
        setNfcData(null);
      }
    } catch (error) {
      if (error && error.message && error.message.includes('cancelled')) {
        console.log('NFC tarama iptal edildi');
        return;
      }
      Alert.alert('NFC Hatası', error?.message || 'NFC tarama sırasında bir hata oluştu!');
      setNfcData(null);
    } finally {
      try {
        await NfcManager.cancelTechnologyRequest();
      } catch (e) {}
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="light-content" backgroundColor="#1a3579" />
      <View style={styles.container}>
        {/* ÜST MAVİ BAR ve LOGO */}
        <View style={styles.header}>
          <Image
            source={require('../assets/images/logo_light.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer} keyboardShouldPersistTaps="handled">
          <Text style={styles.title}>Ödeme</Text>
          <Text style={styles.sectionTitle}>Kart Bilgileri*</Text>

          <TextInput style={styles.input} placeholder="Kart Üzerindeki İsim" value={isim} onChangeText={setIsim} />
          <TextInput style={styles.input} placeholder="Kart Numarası" value={kartNo} onChangeText={setKartNo} keyboardType="numeric" />
          <View style={styles.row}>
            <TextInput style={[styles.input, styles.thirdInput]} placeholder="Ay (AA)" value={ay} onChangeText={setAy} keyboardType="numeric" />
            <TextInput style={[styles.input, styles.thirdInput]} placeholder="Yıl (YY)" value={yil} onChangeText={setYil} keyboardType="numeric" />
            <TextInput style={[styles.input, styles.thirdInput]} placeholder="CCV*" value={ccv} onChangeText={setCcv} keyboardType="numeric" />
          </View>

          <Text style={styles.sectionTitle}>Kart Ekleme Yöntemleri*</Text>
          <View style={styles.nfcRow}>
            <TouchableOpacity
              style={styles.nfcButton}
              onPress={handleNfcPayment}
              disabled={Platform.OS === 'web'}
            >
              <Text style={styles.nfcIcon}>))) </Text>
              <Text style={styles.nfcText}>NFC İle Tara</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.nfcButton, styles.cameraButton]} onPress={() => Alert.alert('Bu özellik eklenmedi.')}>
              <Text style={styles.nfcIcon}>📷 </Text>
              <Text style={styles.nfcText}>Kartı Tara</Text>
            </TouchableOpacity>
          </View>

          {/* NFC'den gelen kart bilgisini ekranda göster */}
          {nfcData && (
            <View style={styles.nfcCardBox}>
              <Text style={styles.nfcCardTitle}>NFC'den Okunan Kart:</Text>
              <Text>İsim: {nfcData.isim || '-'}</Text>
              <Text>Kart No: {nfcData.kartNo || '-'}</Text>
              <Text>Ay: {nfcData.ay || '-'}</Text>
              <Text>Yıl: {nfcData.yil || '-'}</Text>
              <Text>CCV: {nfcData.ccv || '-'}</Text>
            </View>
          )}

          {/* Kart Görsel Alanı */}
          <View style={styles.cardPreview}>
            <View style={styles.cardHeader}>
              <View style={styles.cardChip}></View>
              <Text style={styles.visaText}>VISA</Text>
            </View>
            <Text style={styles.cardNumber}>################</Text>
            <Text style={styles.cardName}>İsim Soyisim</Text>
          </View>

          <View style={styles.paymentButtons}>
            <TouchableOpacity style={styles.payButton} onPress={() => Alert.alert('Ödeme tamamlandı!')}>
              <Text style={styles.lockIcon}>🔒 </Text>
              <Text style={styles.payButtonText}>Ödeme Yap</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.bkmButton} onPress={() => Alert.alert('BKM Express ile ödeme!')}>
              <Text style={styles.bkmText}>BKM express İLE ÖDE</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const HEADER_HEIGHT = 100;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    width: width,
    height: HEADER_HEIGHT,
    backgroundColor: '#1a3579',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingTop: 40,
    paddingLeft: 10,
  },
  logo: {
    width: 150,
    height: 45,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
    color: '#000'
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 15,
    alignSelf: 'flex-start',
    width: '100%',
    maxWidth: 400,
    color: '#333'
  },
  input: {
    width: '100%',
    maxWidth: 400,
    height: 50,
    borderColor: '#000',
    borderWidth: 2,
    borderRadius: 25,
    paddingHorizontal: 16,
    marginBottom: 15,
    fontSize: 16,
    backgroundColor: '#fff'
  },
  row: {
    flexDirection: 'row',
    width: '100%',
    maxWidth: 400,
    justifyContent: 'space-between',
  },
  thirdInput: {
    width: '30%',
    maxWidth: 120,
  },
  nfcRow: {
    flexDirection: 'row',
    width: '100%',
    maxWidth: 400,
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  nfcButton: {
    width: '48%',
    backgroundColor: '#fff',
    borderColor: '#000',
    borderWidth: 2,
    borderRadius: 25,
    alignItems: 'center',
    paddingVertical: 15,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cameraButton: {
    backgroundColor: '#fff',
  },
  nfcCardBox: {
    width: '100%',
    maxWidth: 400,
    backgroundColor: '#e6f0ff',
    borderRadius: 10,
    padding: 15,
    marginVertical: 15,
    borderWidth: 1,
    borderColor: '#1a73e8',
  },
  nfcCardTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#1a3579'
  },
  nfcIcon: {
    fontSize: 16,
    marginRight: 5,
  },
  nfcText: {
    color: '#000',
    fontWeight: '600',
    fontSize: 14,
  },
  cardPreview: {
    width: '100%',
    maxWidth: 400,
    height: 200,
    backgroundColor: '#e0e0e0',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardChip: {
    width: 40,
    height: 30,
    backgroundColor: '#333',
    borderRadius: 5,
  },
  visaText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a73e8',
  },
  cardNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    letterSpacing: 2,
  },
  cardName: {
    fontSize: 14,
    color: '#666',
  },
  paymentButtons: {
    width: '100%',
    maxWidth: 400,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  payButton: {
    width: '48%',
    backgroundColor: '#e53935',
    borderRadius: 12,
    alignItems: 'center',
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  bkmButton: {
    width: '48%',
    backgroundColor: '#e53935',
    borderRadius: 12,
    alignItems: 'center',
    paddingVertical: 16,
  },
  lockIcon: {
    color: '#fff',
    fontSize: 16,
    marginRight: 5,
  },
  payButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  bkmText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
    textAlign: 'center',
  }
});
