{"expo": {"name": "NlkPay", "slug": "NlkPay", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "nlkpay", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NFCReaderUsageDescription": "Bu uygulama ödeme kartlarını okumak için NFC kullanır."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.NFC", "android.permission.VIBRATE"], "intentFilters": [{"action": "android.nfc.action.NDEF_DISCOVERED", "category": ["android.intent.category.DEFAULT"], "data": {"mimeType": "*/*"}}], "package": "com.zeliha_y.Nlk<PERSON>ay"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "425d7a7b-e7cd-4bcb-a679-d9827dd5f04c"}}}}